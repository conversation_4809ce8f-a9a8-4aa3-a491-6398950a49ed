{"roots": ["integration_test_macos"], "packages": [{"name": "integration_test_macos", "version": "0.0.1+1", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"], "devDependencies": ["pedantic"]}, {"name": "pedantic", "version": "1.11.1", "dependencies": []}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}], "configVersion": 1}